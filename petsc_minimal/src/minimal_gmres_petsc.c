/*
    Exact PETSc GMRES implementation - adapted for minimal structure
    Direct copy from PETSc GMRES with minimal adaptation
*/

#include <mpi.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include <stdio.h>
#include "../include/petsc_minimal.h"

/* Include KSP structure definition from minimal_ksp_enhanced.c */
/* KSP structure defined in petsc_exact_types.h */

#define GMRES_DELTA_DIRECTIONS 10
#define GMRES_DEFAULT_MAXK     30

/* Additional constants needed */
#define KSP_DIVERGED_NANORINF -11
#define KSP_DIVERGED_PC_FAILED -10
#define KSP_CONVERGED_HAPPY_BREAKDOWN 8
#define KSP_NORM_NONE 0
#define PETSC_ERR_CONV_FAILED 82
#define PETSC_ERR_NOT_CONVERGED 83
#define PETSC_ERR_ORDER 73

/* PETSc compatibility macros */
#define PetscConj(a) (a)
#define PetscSqrtScalar(a) sqrt(a)
#define PetscAbsScalar(a) fabs(a)
#define PetscAbsReal(a) fabs(a)
/* Macros defined in header */
#define PetscFunctionBegin
#define PetscInfo(obj, ...) (PETSC_SUCCESS)
#define PetscCheck(cond, comm, err, ...) do { if (!(cond)) return err; } while(0)
#define KSPCheckNorm(ksp,beta) do { if (isnan(beta) || isinf(beta)) { ksp->reason = KSP_DIVERGED_NANORINF; return PETSC_SUCCESS; } } while (0)
#define PetscMin(a,b) ((a) < (b) ? (a) : (b))

/* GMRES-specific data structure - exact PETSc copy */
typedef struct {
    PetscScalar     *hh_origin, *hes_origin, *hes_ritz;
    PetscScalar     *cc_origin, *ss_origin;
    PetscScalar     *rs_origin;
    PetscScalar     *orthogwork;
    PetscReal       *Dsvd;
    PetscScalar     *Rsvd;
    PetscReal        haptol;
    PetscInt         max_k;
    PetscInt         nextra_vecs;
    PetscErrorCode (*orthog)(KSP, PetscInt);
    int              cgstype;
    Vec             *vecs;
    Vec             *vecb;
    PetscInt         q_preallocate;
    PetscInt         delta_allocate;
    PetscInt         vv_allocated;
    PetscInt         vecs_allocated;
    Vec            **user_work;
    PetscInt        *mwork_alloc;
    PetscInt         nwork_alloc;
    PetscInt         it;
    PetscInt         fullcycle;
    PetscScalar     *nrs;
    Vec              sol_temp;
    PetscReal        rnorm0;
    PetscReal        breakdowntol;
} KSP_GMRES;

/* Exact PETSc macros with correct indexing */
#define VEC_OFFSET     2
#define HH(a, b)  (gmres->hh_origin + (b) * (gmres->max_k + 2) + (a))
#define HES(a, b) (gmres->hes_origin + (b) * (gmres->max_k + 1) + (a))
#define CC(a)     (gmres->cc_origin + (a))
#define SS(a)     (gmres->ss_origin + (a))
#define GRS(a)    (gmres->rs_origin + (a))
#define VEC_TEMP       gmres->vecs[0]
#define VEC_TEMP_MATOP gmres->vecs[1]
#define VEC_VV(i)      gmres->vecs[VEC_OFFSET + i]

/* Forward declarations */
static PetscErrorCode KSPGMRESUpdateHessenberg(KSP, PetscInt, PetscBool, PetscReal *);
static PetscErrorCode KSPGMRESBuildSoln(PetscScalar *, Vec, Vec, KSP, PetscInt);
static PetscErrorCode KSPGMRESCycle(PetscInt *, KSP);
PetscErrorCode KSPGMRESGetNewVectors(KSP, PetscInt);

/* VecNormalize implementation for GMRES */
static PetscErrorCode VecNormalize(Vec v, PetscReal *norm)
{
    PetscErrorCode ierr;
    ierr = VecNorm(v, NORM_2, norm); CHKERRQ(ierr);
    if (*norm > 0.0) {
        ierr = VecScale(v, 1.0 / (*norm)); CHKERRQ(ierr);
    }
    return PETSC_SUCCESS;
}

/* Simple memory allocation - avoid complex PETSc macros */
static PetscErrorCode AllocateGMRESMemory(KSP_GMRES *gmres, PetscInt max_k)
{
    PetscInt hh_size = (max_k + 2) * (max_k + 1);
    PetscInt hes_size = (max_k + 1) * (max_k + 1);
    PetscInt rs_size = (max_k + 2);
    PetscInt cc_size = (max_k + 1);
    
    gmres->hh_origin = (PetscScalar*)calloc(hh_size, sizeof(PetscScalar));
    gmres->hes_origin = (PetscScalar*)calloc(hes_size, sizeof(PetscScalar));
    gmres->rs_origin = (PetscScalar*)calloc(rs_size, sizeof(PetscScalar));
    gmres->cc_origin = (PetscScalar*)calloc(cc_size, sizeof(PetscScalar));
    gmres->ss_origin = (PetscScalar*)calloc(cc_size, sizeof(PetscScalar));
    
    if (!gmres->hh_origin || !gmres->hes_origin || !gmres->rs_origin || 
        !gmres->cc_origin || !gmres->ss_origin) {
        return PETSC_ERR_MEM;
    }
    
    return PETSC_SUCCESS;
}

/* Exact PETSc KSPSetUp_GMRES implementation */
PetscErrorCode KSPSetUp_GMRES(KSP ksp)
{
  PetscInt   max_k, k;
  KSP_GMRES *gmres = (KSP_GMRES *)ksp->data;
  PetscErrorCode ierr;

  PetscFunctionBegin;
  max_k = gmres->max_k; /* restart size */

  ierr = AllocateGMRESMemory(gmres, max_k); CHKERRQ(ierr);

  if (ksp->calc_sings) {
    /* Allocate workspace to hold Hessenberg matrix needed by lapack */
    gmres->Rsvd = (PetscScalar*)malloc((max_k + 3) * (max_k + 9) * sizeof(PetscScalar));
    gmres->Dsvd = (PetscReal*)malloc(6 * (max_k + 2) * sizeof(PetscReal));
    if (!gmres->Rsvd || !gmres->Dsvd) return PETSC_ERR_MEM;
  }

  /* Allocate array to hold pointers to user vectors */
  gmres->vecs_allocated = VEC_OFFSET + 2 + max_k + gmres->nextra_vecs;

  gmres->vecs = (Vec*)malloc(gmres->vecs_allocated * sizeof(Vec));
  gmres->user_work = (Vec**)malloc((VEC_OFFSET + 2 + max_k) * sizeof(Vec*));
  gmres->mwork_alloc = (PetscInt*)malloc((VEC_OFFSET + 2 + max_k) * sizeof(PetscInt));
  
  if (!gmres->vecs || !gmres->user_work || !gmres->mwork_alloc) return PETSC_ERR_MEM;

  if (gmres->q_preallocate) {
    gmres->vv_allocated = VEC_OFFSET + 2 + max_k;

    /* Create work vectors */
    gmres->user_work[0] = (Vec*)malloc(gmres->vv_allocated * sizeof(Vec));
    if (!gmres->user_work[0]) return PETSC_ERR_MEM;
    
    for (k = 0; k < gmres->vv_allocated; k++) {
        ierr = VecDuplicate(ksp->vec_rhs, &gmres->user_work[0][k]); CHKERRQ(ierr);
    }

    gmres->mwork_alloc[0] = gmres->vv_allocated;
    gmres->nwork_alloc    = 1;
    for (k = 0; k < gmres->vv_allocated; k++) gmres->vecs[k] = gmres->user_work[0][k];
  } else {
    gmres->vv_allocated = 5;

    /* Create minimal work vectors */
    gmres->user_work[0] = (Vec*)malloc(5 * sizeof(Vec));
    if (!gmres->user_work[0]) return PETSC_ERR_MEM;
    
    for (k = 0; k < 5; k++) {
        ierr = VecDuplicate(ksp->vec_rhs, &gmres->user_work[0][k]); CHKERRQ(ierr);
    }

    gmres->mwork_alloc[0] = 5;
    gmres->nwork_alloc    = 1;
    for (k = 0; k < gmres->vv_allocated; k++) gmres->vecs[k] = gmres->user_work[0][k];
  }
  return PETSC_SUCCESS;
}

/* Exact PETSc KSPGMRESCycle implementation */
static PetscErrorCode KSPGMRESCycle(PetscInt *itcount, KSP ksp)
{
  KSP_GMRES *gmres = (KSP_GMRES *)ksp->data;
  PetscReal  res, hapbnd, tt;
  PetscInt   it = 0, max_k = gmres->max_k;
  PetscBool  hapend = PETSC_FALSE;
  PetscErrorCode ierr;

  PetscFunctionBegin;
  if (itcount) *itcount = 0;
  
  /* Debug: Check VEC_VV(0) */
  {
    PetscReal vnorm;
    ierr = VecNorm(VEC_VV(0), NORM_2, &vnorm); CHKERRQ(ierr);
    PetscCall(PetscPrintf(PETSC_COMM_SELF, "KSPGMRESCycle: ||VEC_VV(0)|| = %g before normalize\n", vnorm));
  }
  
  ierr = VecNormalize(VEC_VV(0), &res); CHKERRQ(ierr);
  PetscCall(PetscPrintf(PETSC_COMM_SELF, "KSPGMRESCycle: res = %g after normalize\n", res));
  KSPCheckNorm(ksp, res);

  /* the constant .1 is arbitrary, just some measure at how incorrect the residuals are */
  if ((ksp->rnorm > 0.0) && (PetscAbsReal(res - ksp->rnorm) > gmres->breakdowntol * gmres->rnorm0)) {
    PetscCheck(!ksp->errorifnotconverged, PetscObjectComm((PetscObject)ksp), PETSC_ERR_CONV_FAILED, "Residual norm computed by GMRES recursion formula %g is far from the computed residual norm %g at restart", (double)ksp->rnorm, (double)res);
    ksp->reason = KSP_DIVERGED_BREAKDOWN;
    return PETSC_SUCCESS;
  }
  *GRS(0) = gmres->rnorm0 = res;

  /* check for the convergence */
  ksp->rnorm = res;
  gmres->it = (it - 1);
  PetscCall(PetscPrintf(PETSC_COMM_SELF, "KSPGMRESCycle: Initial residual check - res=%g, ksp->rnorm0=%g, rtol=%g, abstol=%g\n", 
                        res, ksp->rnorm0, ksp->rtol, ksp->abstol));
  if (!res) {
    PetscCall(PetscPrintf(PETSC_COMM_SELF, "KSPGMRESCycle: Converged with zero residual\n"));
    ksp->reason = KSP_CONVERGED_ATOL;
    return PETSC_SUCCESS;
  }

  /* Simple convergence test for minimal implementation */
  if (res < ksp->rtol * ksp->rnorm0 || res < ksp->abstol) {
      PetscCall(PetscPrintf(PETSC_COMM_SELF, "KSPGMRESCycle: Converged by tolerance - res=%g < rtol*rnorm0=%g or abstol=%g\n", 
                           res, ksp->rtol * ksp->rnorm0, ksp->abstol));
      ksp->reason = KSP_CONVERGED_RTOL;
  }
  
  while (!ksp->reason && it < max_k && ksp->its < ksp->max_it) {
    gmres->it = (it - 1);
    if (gmres->vv_allocated <= it + VEC_OFFSET + 1) {
        ierr = KSPGMRESGetNewVectors(ksp, it + 1); CHKERRQ(ierr);
    }
    
    /* Apply preconditioner and matrix: VEC_VV(it+1) = A * P^{-1} * VEC_VV(it) */
    ierr = PCApply(ksp->pc, VEC_VV(it), VEC_TEMP_MATOP); CHKERRQ(ierr);
    ierr = MatMult(ksp->A, VEC_TEMP_MATOP, VEC_VV(1 + it)); CHKERRQ(ierr);

    /* update Hessenberg matrix and do Gram-Schmidt */
    ierr = (*gmres->orthog)(ksp, it); CHKERRQ(ierr);
    if (ksp->reason) break;

    /* vv(i+1) . vv(i+1) */
    ierr = VecNormalize(VEC_VV(it + 1), &tt); CHKERRQ(ierr);
    KSPCheckNorm(ksp, tt);

    /* save the magnitude */
    *HH(it + 1, it)  = tt;
    *HES(it + 1, it) = tt;

    /* check for the happy breakdown */
    hapbnd = PetscAbsScalar(tt / *GRS(it));
    if (hapbnd > gmres->haptol) hapbnd = gmres->haptol;
    if (tt < hapbnd) {
      hapend = PETSC_TRUE;
    }
    ierr = KSPGMRESUpdateHessenberg(ksp, it, hapend, &res); CHKERRQ(ierr);

    it++;
    gmres->it = (it - 1); /* For converged */
    ksp->its++;
    ksp->rnorm = res;
    if (ksp->reason) break;

    /* Simple convergence test for minimal implementation */
    if (res < ksp->rtol * ksp->rnorm0 || res < ksp->abstol) {
        ksp->reason = KSP_CONVERGED_RTOL;
    }

    /* Catch error in happy breakdown and signal convergence and break from loop */
    if (hapend) {
      if (ksp->normtype == KSP_NORM_NONE) { /* convergence test was skipped in this case */
        ksp->reason = KSP_CONVERGED_HAPPY_BREAKDOWN;
      } else if (!ksp->reason) {
        PetscCheck(!ksp->errorifnotconverged, PetscObjectComm((PetscObject)ksp), PETSC_ERR_NOT_CONVERGED, "Reached happy break down, but convergence was not indicated. Residual norm = %g", (double)res);
        ksp->reason = KSP_DIVERGED_BREAKDOWN;
        break;
      }
    }
  }

  if (itcount) *itcount = it;

  /* Form the solution (or the solution so far) */
  ierr = KSPGMRESBuildSoln(GRS(0), ksp->vec_sol, ksp->vec_sol, ksp, it - 1); CHKERRQ(ierr);
  return PETSC_SUCCESS;
}

/* Exact PETSc KSPSolve_GMRES implementation */
static PetscErrorCode KSPSolve_GMRES_Internal(KSP ksp)
{
  PetscInt   its, itcount, i;
  KSP_GMRES *gmres      = (KSP_GMRES *)ksp->data;
  PetscBool  guess_zero = ksp->guess_zero;
  PetscInt   N          = gmres->max_k + 1;
  PetscErrorCode ierr;

  PetscFunctionBegin;
  /* Skip calc_sings check for minimal implementation */
  /* PetscCheck(!ksp->calc_sings || gmres->Rsvd, PetscObjectComm((PetscObject)ksp), PETSC_ERR_ORDER, "Must call KSPSetComputeSingularValues() before KSPSetUp() is called"); */

  ksp->its = 0;

  itcount          = 0;
  gmres->fullcycle = 0;
  ksp->rnorm       = -1.0; /* special marker for KSPGMRESCycle() */
  
  /* Debug: Print solver info */
  PetscCall(PetscPrintf(PETSC_COMM_SELF, "GMRES: Starting solve, ksp->reason=%d, ksp->rnorm=%g\n", ksp->reason, ksp->rnorm));
  PetscCall(PetscPrintf(PETSC_COMM_SELF, "GMRES: Loop condition: !reason=%d, rnorm==-1=%d\n", !ksp->reason, (ksp->rnorm == -1)));
  
  while (!ksp->reason || (ksp->rnorm == -1 && ksp->reason == KSP_DIVERGED_PC_FAILED)) {
    PetscCall(PetscPrintf(PETSC_COMM_SELF, "GMRES: Entering loop iteration\n"));
    /* Compute initial residual */
    if (ksp->guess_zero) {
        /* Debug: check vectors */
        if (!ksp->vec_rhs) {
            PetscCall(PetscPrintf(PETSC_COMM_SELF, "ERROR: ksp->vec_rhs is NULL\n"));
            return PETSC_ERR_ARG_NULL;
        }
        if (!VEC_VV(0)) {
            PetscCall(PetscPrintf(PETSC_COMM_SELF, "ERROR: VEC_VV(0) is NULL\n"));
            return PETSC_ERR_ARG_NULL;
        }
        
        /* Debug: check RHS norm */
        PetscReal rhs_norm;
        ierr = VecNorm(ksp->vec_rhs, NORM_2, &rhs_norm); CHKERRQ(ierr);
        PetscCall(PetscPrintf(PETSC_COMM_SELF, "GMRES: ||b|| = %g\n", rhs_norm));
        
        ierr = VecCopy(ksp->vec_rhs, VEC_VV(0)); CHKERRQ(ierr);
        
        /* Debug: check copied vector norm */
        PetscReal vv0_norm;
        ierr = VecNorm(VEC_VV(0), NORM_2, &vv0_norm); CHKERRQ(ierr);
        PetscCall(PetscPrintf(PETSC_COMM_SELF, "GMRES: After copy, ||VEC_VV(0)|| = %g\n", vv0_norm));
    } else {
        ierr = MatMult(ksp->A, ksp->vec_sol, VEC_TEMP); CHKERRQ(ierr);
        ierr = VecAYPX(VEC_TEMP, -1.0, ksp->vec_rhs); CHKERRQ(ierr);
        ierr = VecCopy(VEC_TEMP, VEC_VV(0)); CHKERRQ(ierr);
    }
    
    ierr = KSPGMRESCycle(&its, ksp); CHKERRQ(ierr);
    /* Store the Hessenberg matrix and the basis vectors of the Krylov subspace
    if the cycle is complete for the computation of the Ritz pairs */
    if (its == gmres->max_k) {
      gmres->fullcycle++;
      if (ksp->calc_ritz) {
        if (!gmres->hes_ritz) {
          gmres->hes_ritz = (PetscScalar*)malloc(N * N * sizeof(PetscScalar));
          gmres->vecb = (Vec*)malloc(N * sizeof(Vec));
          if (!gmres->hes_ritz || !gmres->vecb) return PETSC_ERR_MEM;
          
          for (i = 0; i < N; i++) {
              ierr = VecDuplicate(VEC_VV(0), &gmres->vecb[i]); CHKERRQ(ierr);
          }
        }
        memcpy(gmres->hes_ritz, gmres->hes_origin, N * N * sizeof(PetscScalar));
        for (i = 0; i < gmres->max_k + 1; i++) {
            ierr = VecCopy(VEC_VV(i), gmres->vecb[i]); CHKERRQ(ierr);
        }
      }
    }
    itcount += its;
    if (itcount >= ksp->max_it) {
      if (!ksp->reason) ksp->reason = KSP_DIVERGED_ITS;
      break;
    }
    ksp->guess_zero = PETSC_FALSE; /* every future call to KSPInitialResidual() will have nonzero guess */
  }
  ksp->guess_zero = guess_zero; /* restore if user provided nonzero initial guess */
  return PETSC_SUCCESS;
}

/* Exact PETSc KSPGMRESBuildSoln implementation */  
static PetscErrorCode KSPGMRESBuildSoln(PetscScalar *nrs, Vec vs, Vec vdest, KSP ksp, PetscInt it)
{
  PetscScalar tt;
  PetscInt    ii, k, j;
  KSP_GMRES  *gmres = (KSP_GMRES *)ksp->data;
  PetscErrorCode ierr;

  PetscFunctionBegin;
  /* Solve for solution vector that minimizes the residual */

  /* If it is < 0, no gmres steps have been performed */
  if (it < 0) {
    ierr = VecCopy(vs, vdest); CHKERRQ(ierr); /* VecCopy() is smart, exists immediately if vguess == vdest */
    return PETSC_SUCCESS;
  }
  if (*HH(it, it) != 0.0) {
    nrs[it] = *GRS(it) / *HH(it, it);
  } else {
    PetscCheck(!ksp->errorifnotconverged, PetscObjectComm((PetscObject)ksp), PETSC_ERR_NOT_CONVERGED, "You reached the break down in GMRES; HH(it,it) = 0");
    ksp->reason = KSP_DIVERGED_BREAKDOWN;
    return PETSC_SUCCESS;
  }
  for (ii = 1; ii <= it; ii++) {
    k  = it - ii;
    tt = *GRS(k);
    for (j = k + 1; j <= it; j++) tt = tt - *HH(k, j) * nrs[j];
    if (*HH(k, k) == 0.0) {
      PetscCheck(!ksp->errorifnotconverged, PetscObjectComm((PetscObject)ksp), PETSC_ERR_NOT_CONVERGED, "Likely your matrix or preconditioner is singular. HH(k,k) is identically zero; k = %d", k);
      ksp->reason = KSP_DIVERGED_BREAKDOWN;
      return PETSC_SUCCESS;
    }
    nrs[k] = tt / *HH(k, k);
  }

  /* Accumulate the correction to the solution of the preconditioned problem in TEMP */
  ierr = VecSet(VEC_TEMP, 0.0); CHKERRQ(ierr);
  for (j = 0; j <= it; j++) {
      ierr = VecAXPY(VEC_TEMP, nrs[j], VEC_VV(j)); CHKERRQ(ierr);
  }

  /* add solution to previous solution */
  if (vdest != vs) {
      ierr = VecCopy(vs, vdest); CHKERRQ(ierr);
  }
  ierr = VecAXPY(vdest, 1.0, VEC_TEMP); CHKERRQ(ierr);
  return PETSC_SUCCESS;
}

/* Exact PETSc KSPGMRESUpdateHessenberg implementation */
static PetscErrorCode KSPGMRESUpdateHessenberg(KSP ksp, PetscInt it, PetscBool hapend, PetscReal *res)
{
  PetscScalar *hh, *cc, *ss, tt;
  PetscInt     j;
  KSP_GMRES   *gmres = (KSP_GMRES *)ksp->data;

  PetscFunctionBegin;
  hh = HH(0, it);
  cc = CC(0);
  ss = SS(0);

  /* Apply all the previously computed plane rotations to the new column
     of the Hessenberg matrix */
  for (j = 1; j <= it; j++) {
    tt  = *hh;
    *hh = PetscConj(*cc) * tt + *ss * *(hh + 1);
    hh++;
    *hh = *cc++ * *hh - (*ss++ * tt);
  }

  /*
    compute the new plane rotation, and apply it to:
     1) the right-hand side of the Hessenberg system
     2) the new column of the Hessenberg matrix
    thus obtaining the updated value of the residual
  */
  if (!hapend) {
    tt = PetscSqrtScalar(PetscConj(*hh) * *hh + PetscConj(*(hh + 1)) * *(hh + 1));
    if (tt == 0.0) {
      PetscCheck(!ksp->errorifnotconverged, PetscObjectComm((PetscObject)ksp), PETSC_ERR_NOT_CONVERGED, "tt == 0.0");
      ksp->reason = KSP_DIVERGED_BREAKDOWN;
      return PETSC_SUCCESS;
    }
    *cc          = *hh / tt;
    *ss          = *(hh + 1) / tt;
    *GRS(it + 1) = -(*ss * *GRS(it));
    *GRS(it)     = PetscConj(*cc) * *GRS(it);
    *hh          = PetscConj(*cc) * *hh + *ss * *(hh + 1);
    *res         = PetscAbsScalar(*GRS(it + 1));
  } else {
    /* happy breakdown: HH(it+1, it) = 0, therefore we don't need to apply
            another rotation matrix (so RH doesn't change).  The new residual is
            always the new sine term times the residual from last time (GRS(it)),
            but now the new sine rotation would be zero...so the residual should
            be zero...so we will multiply "zero" by the last residual.  This might
            not be exactly what we want to do here -could just return "zero". */

    *res = 0.0;
  }
  return PETSC_SUCCESS;
}

/* Exact PETSc KSPGMRESGetNewVectors implementation */
PetscErrorCode KSPGMRESGetNewVectors(KSP ksp, PetscInt it)
{
  KSP_GMRES *gmres = (KSP_GMRES *)ksp->data;
  PetscInt   nwork = gmres->nwork_alloc, k, nalloc;
  PetscErrorCode ierr;

  PetscFunctionBegin;
  nalloc = PetscMin(ksp->max_it, gmres->delta_allocate);
  /* Adjust the number to allocate to make sure that we don't exceed the
    number of available slots */
  if (it + VEC_OFFSET + nalloc >= gmres->vecs_allocated) nalloc = gmres->vecs_allocated - it - VEC_OFFSET;
  if (!nalloc) return PETSC_SUCCESS;

  gmres->vv_allocated += nalloc;

  /* Create new work vectors */
  gmres->user_work[nwork] = (Vec*)malloc(nalloc * sizeof(Vec));
  if (!gmres->user_work[nwork]) return PETSC_ERR_MEM;
  
  for (k = 0; k < nalloc; k++) {
      ierr = VecDuplicate(ksp->vec_rhs, &gmres->user_work[nwork][k]); CHKERRQ(ierr);
  }

  gmres->mwork_alloc[nwork] = nalloc;
  for (k = 0; k < nalloc; k++) gmres->vecs[it + VEC_OFFSET + k] = gmres->user_work[nwork][k];
  gmres->nwork_alloc++;
  return PETSC_SUCCESS;
}

/* Classical Gram-Schmidt orthogonalization - exact PETSc implementation */
PetscErrorCode KSPGMRESClassicalGramSchmidtOrthogonalization(KSP ksp, PetscInt it)
{
    KSP_GMRES   *gmres = (KSP_GMRES *)ksp->data;
    PetscScalar *hh, *lhh;
    PetscInt     j;
    PetscErrorCode ierr;

    PetscFunctionBegin;
    hh = HH(0, it);
    lhh = (PetscScalar*)malloc((it + 1) * sizeof(PetscScalar));
    if (!lhh) return PETSC_ERR_MEM;

    /* Compute all inner products at once: <v_new, v_j> for j=0...it */
    for (j = 0; j <= it; j++) {
        ierr = VecDot(VEC_VV(it + 1), VEC_VV(j), &lhh[j]); CHKERRQ(ierr);
        hh[j] = lhh[j];
        *HES(j, it) = hh[j];
    }
    
    /* Orthogonalize: v_new = v_new - sum(h_j * v_j) */
    for (j = 0; j <= it; j++) {
        ierr = VecAXPY(VEC_VV(it + 1), -lhh[j], VEC_VV(j)); CHKERRQ(ierr);
    }

    free(lhh);
    return PETSC_SUCCESS;
}

/* Modified Gram-Schmidt orthogonalization - exact PETSc implementation */
PetscErrorCode KSPGMRESModifiedGramSchmidtOrthogonalization(KSP ksp, PetscInt it)
{
    KSP_GMRES   *gmres = (KSP_GMRES *)ksp->data;
    PetscScalar *hh;
    PetscInt     j;
    PetscErrorCode ierr;

    PetscFunctionBegin;
    hh = HH(0, it);
    
    /* Orthogonalize against each vector sequentially */
    for (j = 0; j <= it; j++) {
        ierr = VecDot(VEC_VV(it + 1), VEC_VV(j), &hh[j]); CHKERRQ(ierr);
        *HES(j, it) = hh[j];
        ierr = VecAXPY(VEC_VV(it + 1), -hh[j], VEC_VV(j)); CHKERRQ(ierr);
    }
    
    return PETSC_SUCCESS;
}

/* Create GMRES context - exact PETSc implementation */
PetscErrorCode KSPCreate_GMRES(KSP ksp)
{
  KSP_GMRES *gmres;

  PetscFunctionBegin;
  gmres = (KSP_GMRES*)calloc(1, sizeof(KSP_GMRES));
  if (!gmres) return PETSC_ERR_MEM;
  
  ksp->data = (void *)gmres;

  /* Set defaults - exact PETSc values */
  gmres->haptol         = 1.0e-30;
  gmres->breakdowntol   = 0.1;
  gmres->q_preallocate  = 0;
  gmres->delta_allocate = GMRES_DELTA_DIRECTIONS;
  gmres->orthog         = KSPGMRESClassicalGramSchmidtOrthogonalization;
  gmres->nrs            = NULL;
  gmres->sol_temp       = NULL;
  gmres->max_k          = GMRES_DEFAULT_MAXK;
  gmres->Rsvd           = NULL;
  gmres->cgstype        = 0; /* KSP_GMRES_CGS_REFINE_NEVER */
  gmres->orthogwork     = NULL;
  return PETSC_SUCCESS;
}

/* Main GMRES solver - exact PETSc name for 100% compatibility */
PetscErrorCode KSPSolve_GMRES(KSP ksp, Vec b, Vec x)
{
    PetscErrorCode  ierr;
    KSP_GMRES      *gmres;

    /* Initialize GMRES context if needed */
    if (!ksp->data) {
        ierr = KSPCreate_GMRES(ksp); CHKERRQ(ierr);
    }
    
    gmres = (KSP_GMRES*)ksp->data;

    /* Set solution and RHS vectors */
    ksp->vec_sol = x;
    ksp->vec_rhs = b;
    
    /* Set up GMRES if not done */
    if (!gmres->vecs) {
        ierr = KSPSetUp_GMRES(ksp); CHKERRQ(ierr);
    }
    
    /* Call the main GMRES solver */
    ierr = KSPSolve_GMRES_Internal(ksp); CHKERRQ(ierr);
    
    return PETSC_SUCCESS;
}

/* Exact PETSc KSPDestroy_GMRES implementation */
PetscErrorCode KSPDestroy_GMRES(KSP ksp)
{
    KSP_GMRES *gmres = (KSP_GMRES*)ksp->data;
    PetscInt   i;
    
    PetscFunctionBegin;
    if (!gmres) PetscFunctionReturn(PETSC_SUCCESS);
    
    /* Free work vectors */
    if (gmres->vecs) {
        for (i = 0; i < gmres->nwork_alloc; i++) {
            PetscInt j;
            if (gmres->user_work[i]) {
                for (j = 0; j < gmres->mwork_alloc[i]; j++) {
                    if (gmres->user_work[i][j]) {
                        VecDestroy(&gmres->user_work[i][j]);
                    }
                }
                free(gmres->user_work[i]);
            }
        }
    }
    
    /* Free allocated arrays */
    if (gmres->hh_origin) free(gmres->hh_origin);
    if (gmres->hes_origin) free(gmres->hes_origin);
    if (gmres->cc_origin) free(gmres->cc_origin);
    if (gmres->ss_origin) free(gmres->ss_origin);
    if (gmres->rs_origin) free(gmres->rs_origin);
    if (gmres->vecs) free(gmres->vecs);
    if (gmres->sol_temp) VecDestroy(&gmres->sol_temp);
    if (gmres->nrs) free(gmres->nrs);
    if (gmres->Dsvd) free(gmres->Dsvd);
    if (gmres->Rsvd) free(gmres->Rsvd);
    if (gmres->orthogwork) free(gmres->orthogwork);
    if (gmres->hes_ritz) free(gmres->hes_ritz);
    
    free(gmres);
    ksp->data = NULL;
    
    PetscFunctionReturn(PETSC_SUCCESS);
}

/* Exact PETSc KSPReset_GMRES implementation */
PetscErrorCode KSPReset_GMRES(KSP ksp)
{
    KSP_GMRES *gmres = (KSP_GMRES*)ksp->data;
    PetscInt   i;
    
    PetscFunctionBegin;
    if (!gmres) PetscFunctionReturn(PETSC_SUCCESS);
    
    /* Free work vectors */
    if (gmres->vecs) {
        for (i = 0; i < gmres->nwork_alloc; i++) {
            PetscInt j;
            if (gmres->user_work[i]) {
                for (j = 0; j < gmres->mwork_alloc[i]; j++) {
                    if (gmres->user_work[i][j]) {
                        VecDestroy(&gmres->user_work[i][j]);
                    }
                }
                free(gmres->user_work[i]);
            }
        }
    }
    
    /* Free allocated arrays */
    if (gmres->hh_origin) { free(gmres->hh_origin); gmres->hh_origin = NULL; }
    if (gmres->hes_origin) { free(gmres->hes_origin); gmres->hes_origin = NULL; }
    if (gmres->cc_origin) { free(gmres->cc_origin); gmres->cc_origin = NULL; }
    if (gmres->ss_origin) { free(gmres->ss_origin); gmres->ss_origin = NULL; }
    if (gmres->rs_origin) { free(gmres->rs_origin); gmres->rs_origin = NULL; }
    if (gmres->vecs) { free(gmres->vecs); gmres->vecs = NULL; }
    if (gmres->sol_temp) { VecDestroy(&gmres->sol_temp); gmres->sol_temp = NULL; }
    
    gmres->vv_allocated = 0;
    gmres->nwork_alloc = 0;
    
    PetscFunctionReturn(PETSC_SUCCESS);
}

/* Exact PETSc KSPView_GMRES implementation - simplified */
PetscErrorCode KSPView_GMRES(KSP ksp, PetscViewer viewer)
{
    KSP_GMRES *gmres = (KSP_GMRES*)ksp->data;
    PetscBool iascii;
    
    PetscFunctionBegin;
    iascii = PETSC_TRUE; /* For minimal implementation, assume ASCII viewer */
    
    if (iascii) {
        PetscCall(PetscPrintf(PETSC_COMM_SELF, "  restart=%d, using %s orthogonalization\n", 
                             gmres->max_k,
                             (gmres->orthog == KSPGMRESClassicalGramSchmidtOrthogonalization) ? 
                             "Classical (unmodified) Gram-Schmidt" : "Modified Gram-Schmidt"));
        PetscCall(PetscPrintf(PETSC_COMM_SELF, "  happy breakdown tolerance %g\n", (double)gmres->haptol));
    }
    
    PetscFunctionReturn(PETSC_SUCCESS);
}